#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试申万行业分类功能
使用新的index_member_all接口
"""

import pandas as pd
import tushare as ts
from 下载日数据 import ComprehensiveStockDataProcessor, Config

def test_sw_industry_api():
    """测试申万行业分类API"""
    print("=" * 60)
    print("测试申万行业分类API")
    print("=" * 60)
    
    # 初始化
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    pro = processor.pro
    
    print("\n1. 测试index_member_all接口...")
    
    try:
        # 测试获取单个股票的行业分类
        test_code = "000001.SZ"
        print(f"获取 {test_code} 的行业分类...")
        
        sw_data = pro.index_member_all(ts_code=test_code, is_new='Y')
        
        if sw_data is not None and not sw_data.empty:
            print(f"✓ 成功获取 {len(sw_data)} 条记录")
            print("数据样例:")
            print(sw_data[['l1_name', 'l2_name', 'l3_name', 'ts_code', 'name', 'in_date', 'out_date']].head())
        else:
            print("⚠ 未获取到数据")
        
        print(f"\n2. 测试获取特定行业的成分股...")
        # 测试获取黄金行业的成分股
        gold_data = pro.index_member_all(l3_code='850531.SI', is_new='Y')
        
        if gold_data is not None and not gold_data.empty:
            print(f"✓ 黄金行业包含 {len(gold_data)} 只股票")
            print("黄金行业成分股:")
            print(gold_data[['ts_code', 'name', 'l1_name', 'l2_name', 'l3_name']].head())
        else:
            print("⚠ 未获取到黄金行业数据")
        
        print(f"\n3. 测试批量获取所有行业分类...")
        # 获取所有最新的行业分类数据（限制前100条）
        all_sw_data = pro.index_member_all(is_new='Y')
        
        if all_sw_data is not None and not all_sw_data.empty:
            print(f"✓ 获取到 {len(all_sw_data)} 条行业分类记录")
            
            # 统计各级行业数量
            l1_count = all_sw_data['l1_name'].nunique()
            l2_count = all_sw_data['l2_name'].nunique()
            l3_count = all_sw_data['l3_name'].nunique()
            
            print(f"  - 一级行业: {l1_count} 个")
            print(f"  - 二级行业: {l2_count} 个")
            print(f"  - 三级行业: {l3_count} 个")
            
            print("\n一级行业列表:")
            l1_industries = all_sw_data['l1_name'].unique()
            for i, industry in enumerate(l1_industries, 1):
                print(f"  {i:2d}. {industry}")
        else:
            print("⚠ 未获取到行业分类数据")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sw_industry_methods():
    """测试申万行业分类方法"""
    print("\n" + "=" * 60)
    print("测试申万行业分类方法")
    print("=" * 60)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    # 测试股票列表
    test_stocks = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SH",  # 浦发银行
        "600036.SH",  # 招商银行
        "000858.SZ",  # 五粮液
    ]
    
    trade_date = "20231108"
    
    try:
        print(f"\n1. 测试逐个获取方法...")
        sw_result1 = processor.get_sw_industry(trade_date, test_stocks)
        print(f"✓ 逐个获取完成，共 {len(sw_result1)} 条记录")
        print("结果样例:")
        print(sw_result1)
        
        print(f"\n2. 测试批量获取方法...")
        sw_result2 = processor.get_sw_industry_batch(trade_date, test_stocks)
        print(f"✓ 批量获取完成，共 {len(sw_result2)} 条记录")
        print("结果样例:")
        print(sw_result2)
        
        print(f"\n3. 比较两种方法的结果...")
        # 比较结果是否一致
        comparison_cols = ['新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称']
        
        for col in comparison_cols:
            match_count = (sw_result1[col] == sw_result2[col]).sum()
            total_count = len(sw_result1)
            print(f"  {col}: {match_count}/{total_count} 匹配")
        
        # 保存测试结果
        output_file = f"申万行业测试结果_{trade_date}.csv"
        
        # 合并两种方法的结果进行对比
        comparison_df = sw_result1.copy()
        for col in comparison_cols:
            comparison_df[f"{col}_批量"] = sw_result2[col]
        
        comparison_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 测试结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_with_sw():
    """测试完整数据获取（重点关注申万行业）"""
    print("\n" + "=" * 60)
    print("测试完整数据获取（重点关注申万行业）")
    print("=" * 60)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    trade_date = "20231108"
    
    try:
        print(f"构建 {trade_date} 的完整数据集（限制前20只股票）...")
        df = processor.build_comprehensive_dataset(trade_date, limit=20)
        
        print(f"✓ 数据集构建完成，共 {len(df)} 行数据")
        
        # 检查申万行业数据质量
        sw_cols = ['新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称']
        
        print(f"\n申万行业数据质量统计:")
        for col in sw_cols:
            not_null_count = df[col].notna().sum()
            print(f"  {col}: {not_null_count}/{len(df)} ({not_null_count/len(df)*100:.1f}%)")
        
        # 显示申万行业分布
        print(f"\n一级行业分布:")
        l1_dist = df['新版申万一级行业名称'].value_counts()
        print(l1_dist.head(10))
        
        # 保存结果
        output_file = f"完整数据_含申万行业_{trade_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 完整数据已保存到: {output_file}")
        
        # 显示包含申万行业的样例数据
        print(f"\n样例数据（申万行业相关字段）:")
        sample_cols = ['股票代码', '股票名称'] + sw_cols
        print(df[sample_cols].head(10))
        
        return True
        
    except Exception as e:
        print(f"❌ 完整测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试申万行业分类功能...")
    
    # 1. 测试API
    success1 = test_sw_industry_api()
    
    if success1:
        # 2. 测试方法
        success2 = test_sw_industry_methods()
        
        if success2:
            # 3. 测试完整功能
            success3 = test_comprehensive_with_sw()
            
            if success1 and success2 and success3:
                print("\n🎉 所有申万行业分类测试通过！")
                print("新的index_member_all接口工作正常。")
            else:
                print("\n⚠ 部分测试失败，请检查配置。")
        else:
            print("\n❌ 方法测试失败。")
    else:
        print("\n❌ API测试失败，请检查Tushare权限。")
