
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合股票数据获取脚本
整合QMT和Tushare数据源，获取完整的股票数据集
包含基础行情、财务数据、资金流向、指数成分、行业分类等信息
"""

import datetime as dt
import logging
import os
import sys
from typing import List, Dict, Optional
from dataclasses import dataclass
import warnings

import pandas as pd

# 数据源
from xtquant import xtdata
import tushare as ts

warnings.filterwarnings("ignore")

# ========== 配置参数 ==========
@dataclass
class Config:
    """配置参数类"""
    # Tushare Token
    TUSHARE_TOKEN: str = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"

    # 复权与周期
    DIVIDEND_TYPE: str = 'front'  # front: 前复权; none 不复权
    PERIOD: str = '5m'            # 5分钟K线

    # 下载并发度
    N_JOBS: int = 4

    # 批处理大小
    BATCH_SIZE: int = 50

    # 重试次数
    MAX_RETRIES: int = 3

    # 指数代码映射
    INDEX_CODES: Dict[str, str] = None

    # 特定时间点列表
    TIME_POINTS: List[str] = None

    def __post_init__(self):
        if self.INDEX_CODES is None:
            self.INDEX_CODES = {
                '沪深300成分股': '399300.SZ',
                '上证50成分股': '000016.SH',
                '中证500成分股': '000905.SH',
                '中证1000成分股': '000852.SH',
                '中证2000成分股': '932000.CSI',
                '创业板指成分股': '399006.SZ',
            }

        if self.TIME_POINTS is None:
            self.TIME_POINTS = ['09:35', '09:45', '09:55']


# ========== 日志配置 ==========
def setup_logging(log_level: str = 'INFO') -> logging.Logger:
    """配置日志"""
    logger = logging.getLogger(__name__)
    logger.setLevel(getattr(logging, log_level.upper()))

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger


# ========== 数据获取架构设计 ==========
class ComprehensiveStockDataProcessor:
    """综合股票数据处理器

    数据获取顺序：
    1. 获取股票列表和基础信息 (Tushare)
    2. 下载QMT分钟数据 (QMT)
    3. 提取日内指标 (QMT)
    4. 获取市值数据 (Tushare)
    5. 获取财务数据 (Tushare)
    6. 获取资金流向数据 (Tushare)
    7. 获取指数成分标识 (Tushare)
    8. 获取申万行业分类 (Tushare)
    9. 合并所有数据
    """

    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.logger = setup_logging()
        self._pro = None

    @property
    def pro(self):
        """懒加载Tushare连接"""
        if self._pro is None:
            token = os.getenv('TUSHARE_TOKEN') or self.config.TUSHARE_TOKEN
            if not token:
                raise RuntimeError('未配置 Tushare Token')
            self.logger.info("正在连接Tushare...")
            self._pro = ts.pro_api(token)
            self.logger.info("Tushare连接成功")
        return self._pro

    @staticmethod
    def ymd(date: dt.date) -> str:
        """日期格式化"""
        return date.strftime('%Y%m%d')

    @staticmethod
    def parse_trade_date(trade_date: str) -> dt.date:
        """解析交易日期"""
        return dt.datetime.strptime(trade_date, '%Y%m%d').date()

    def validate_trade_date(self, trade_date: str) -> bool:
        """验证交易日期格式"""
        try:
            self.parse_trade_date(trade_date)
            return True
        except ValueError:
            self.logger.error(f"无效的交易日期格式: {trade_date}")
            return False

    # ========== 1. 股票列表和基础信息 ==========
    def get_stock_list_and_names(self, trade_date: str) -> pd.DataFrame:
        """获取股票列表和名称"""
        try:
            self.logger.info(f"获取 {trade_date} 的股票列表...")
            db = self.pro.daily_basic(
                ts_code='',
                trade_date=trade_date,
                fields='ts_code,trade_date'
            )
            if db is None or db.empty:
                raise RuntimeError(f'在 {trade_date} 未获取到股票列表')

            self.logger.info("获取股票基本信息...")
            basics = self.pro.stock_basic(
                fields='ts_code,name,list_status,exchange'
            )
            df = db.merge(basics[['ts_code', 'name']], on='ts_code', how='left')

            self.logger.info(f"获取到 {len(df)} 只股票")
            return df.rename(columns={'name': '股票名称'})

        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            raise

    # ========== 2. QMT数据下载和处理 ==========
    def download_minute_data(self, stock: str, trade_date: str) -> bool:
        """下载单只股票的分钟数据"""
        try:
            xtdata.download_history_data(
                stock_code=stock,
                period=self.config.PERIOD,
                start_time=trade_date,
                end_time=trade_date
            )
            return True
        except Exception as e:
            self.logger.warning(f'下载失败 {stock}: {e}')
            return False

    def extract_intraday_metrics(self, stock: str, trade_date: str) -> Optional[Dict]:
        """从本地5m数据中抽取当日指标"""
        try:
            data = xtdata.get_local_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount', 'preClose', 'suspendFlag'],
                stock_list=[stock],
                period=self.config.PERIOD,
                dividend_type=self.config.DIVIDEND_TYPE,
            )

            df = data.get(stock)
            if df is None or len(df) == 0:
                return None

            # 转换时间并过滤至当日
            df['datetime'] = df['time'].apply(lambda x: dt.datetime.fromtimestamp(x/1000.0))
            the_date = self.parse_trade_date(trade_date)
            df_today = df[df['datetime'].dt.date == the_date].copy()

            if df_today.empty:
                return None

            df_today.sort_values('datetime', inplace=True)

            # 日级口径（由5m聚合）
            metrics = {
                '股票代码': stock,
                '交易日期': trade_date,
                '开盘价': float(df_today.iloc[0]['open']),
                '最高价': float(df_today['high'].max()),
                '最低价': float(df_today['low'].min()),
                '收盘价': float(df_today.iloc[-1]['close']),
                '前收盘价': float(df_today.iloc[0]['preClose']) if 'preClose' in df_today.columns else None,
                '成交量': int(df_today['volume'].sum()),
                '成交额': float(df_today['amount'].sum()),
            }

            # 获取特定时间点收盘价
            for time_point in self.config.TIME_POINTS:
                close_price = self._get_close_at(df_today, time_point)
                metrics[f'{time_point}收盘价'] = close_price

            return metrics

        except Exception as e:
            self.logger.debug(f'抽取指标失败 {stock}: {e}')
            return None

    def _get_close_at(self, df_today: pd.DataFrame, hhmm: str) -> Optional[float]:
        """获取指定时间点的收盘价"""
        try:
            row = df_today[df_today['datetime'].dt.strftime('%H:%M') == hhmm]
            if row.empty:
                return None
            return float(row.iloc[0]['close'])
        except Exception:
            return None

    # ========== 3. Tushare市值数据 ==========
    def get_daily_basic_mv(self, trade_date: str) -> pd.DataFrame:
        """获取市值数据"""
        try:
            self.logger.info("获取市值数据...")
            df = self.pro.daily_basic(
                ts_code='',
                trade_date=trade_date,
                fields='ts_code,trade_date,circ_mv,total_mv'
            )
            if df is None:
                df = pd.DataFrame()

            return df.rename(columns={
                'ts_code': '股票代码',
                'circ_mv': '流通市值',
                'total_mv': '总市值'
            })
        except Exception as e:
            self.logger.error(f"获取市值数据失败: {e}")
            return pd.DataFrame()

    # ========== 4. Tushare财务数据 ==========
    def get_financials_full(self, trade_date: str, ts_codes: List[str]) -> pd.DataFrame:
        """完整财务数据：净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)"""
        self.logger.info("获取财务数据...")
        rows = []

        for i, code in enumerate(ts_codes):
            if i % 10 == 0:
                self.logger.info(f"财务数据进度: {i}/{len(ts_codes)}")

            try:
                # 利润表：单季归母净利润
                inc = self.pro.income(ts_code=code, report_type='4')
                npp_ttm = None
                npp_q = None
                if inc is not None and not inc.empty:
                    inc = inc.sort_values('end_date')
                    inc = inc[inc['end_date'] <= trade_date]
                    last4 = inc.tail(4)
                    if not last4.empty:
                        if 'n_income_attr_p' in last4.columns:
                            npp_ttm = pd.to_numeric(last4['n_income_attr_p'], errors='coerce').sum()
                            npp_q = pd.to_numeric(last4.iloc[-1]['n_income_attr_p'], errors='coerce')

                # 现金流量表：单季经营现金流净额
                cfo_ttm = None
                cfs = self.pro.cashflow(ts_code=code, report_type='4')
                if cfs is not None and not cfs.empty:
                    cfs = cfs.sort_values('end_date')
                    cfs = cfs[cfs['end_date'] <= trade_date]
                    last4c = cfs.tail(4)
                    if not last4c.empty and 'n_cashflow_act' in last4c.columns:
                        cfo_ttm = pd.to_numeric(last4c['n_cashflow_act'], errors='coerce').sum()

                # 资产负债表：最新一期
                teq = ta = tl = None
                bs = self.pro.balancesheet(ts_code=code)
                if bs is not None and not bs.empty:
                    bs = bs.sort_values('end_date')
                    bs = bs[bs['end_date'] <= trade_date]
                    if not bs.empty:
                        last = bs.iloc[-1]
                        # 尝试多种字段名
                        teq = (
                            last.get('total_hldr_eqy_exc_min_int') or
                            last.get('total_hldr_eqy_excl_min_int') or
                            last.get('total_hldr_eqy_inc_min_int') or
                            last.get('total_owner_equity')
                        )
                        ta = last.get('total_assets')
                        tl = last.get('total_liab')

                        # 若净资产缺失而总资产/总负债存在，则以差额估算
                        try:
                            ta_num = float(ta) if ta is not None else None
                            tl_num = float(tl) if tl is not None else None
                            teq_num = float(teq) if teq is not None else None
                        except Exception:
                            ta_num = tl_num = teq_num = None

                        if teq_num is None and ta_num is not None and tl_num is not None:
                            teq = ta_num - tl_num

                rows.append({
                    '股票代码': code,
                    '净利润TTM': npp_ttm,
                    '现金流TTM': cfo_ttm,
                    '净资产': teq,
                    '总资产': ta,
                    '总负债': tl,
                    '净利润(当季)': npp_q,
                })

            except Exception as e:
                self.logger.warning(f'获取财务失败 {code}: {e}')
                rows.append({
                    '股票代码': code,
                    '净利润TTM': None,
                    '现金流TTM': None,
                    '净资产': None,
                    '总资产': None,
                    '总负债': None,
                    '净利润(当季)': None,
                })

        return pd.DataFrame(rows)

    # ========== 5. Tushare资金流向数据 ==========
    def get_moneyflow(self, trade_date: str) -> pd.DataFrame:
        """获取资金流数据"""
        try:
            self.logger.info("获取资金流数据...")
            mf = self.pro.moneyflow(
                trade_date=trade_date,
                fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
            )
            if mf is None:
                return pd.DataFrame()

            mf = mf.rename(columns={
                'ts_code': '股票代码',
                'buy_sm_amount': '散户资金买入额',
                'sell_sm_amount': '散户资金卖出额',
                'buy_md_amount': '中户资金买入额',
                'sell_md_amount': '中户资金卖出额',
                'buy_lg_amount': '大户资金买入额',
                'sell_lg_amount': '大户资金卖出额',
                'buy_elg_amount': '机构资金买入额',
                'sell_elg_amount': '机构资金卖出额',
            })

            return mf[['股票代码', '散户资金买入额', '散户资金卖出额', '中户资金买入额', '中户资金卖出额',
                      '大户资金买入额', '大户资金卖出额', '机构资金买入额', '机构资金卖出额']]
        except Exception as e:
            self.logger.warning(f"获取资金流数据失败: {e}")
            return pd.DataFrame()

    # ========== 6. Tushare指数成分股标识（优化版） ==========
    def get_index_membership_flags(self, trade_date: str, ts_codes: List[str]) -> pd.DataFrame:
        """获取指数成分标识 - 使用index_weight接口"""
        self.logger.info("获取指数成分标识...")

        # 先构造一个全0的结果框架
        result = pd.DataFrame({'股票代码': ts_codes})
        for col in self.config.INDEX_CODES.keys():
            result[col] = 0

        for col_name, idx_code in self.config.INDEX_CODES.items():
            try:
                self.logger.debug(f"获取 {col_name} ({idx_code}) 成分股...")

                # 使用index_weight接口获取指定日期的成分股
                w = self.pro.index_weight(
                    index_code=idx_code,
                    trade_date=trade_date
                )

                if w is not None and not w.empty:
                    # 获取成分股代码
                    members = set(w['con_code'].unique())
                    self.logger.debug(f"{col_name} 包含 {len(members)} 只成分股")

                    # 标记成分股
                    if members:
                        mask = result['股票代码'].isin(members)
                        result.loc[mask, col_name] = 1
                        marked_count = mask.sum()
                        self.logger.debug(f"{col_name} 标记了 {marked_count} 只股票")
                else:
                    self.logger.warning(f"未获取到 {col_name} ({idx_code}) 在 {trade_date} 的成分股数据")

            except Exception as e:
                self.logger.warning(f'获取指数成分失败 {col_name} ({idx_code}): {e}')

        return result[['股票代码'] + list(self.config.INDEX_CODES.keys())]

    def get_index_membership_flags_batch(self, trade_date: str, ts_codes: List[str]) -> pd.DataFrame:
        """批量获取指数成分标识 - 备用方法"""
        self.logger.info("批量获取指数成分标识...")

        # 先构造一个全0的结果框架
        result = pd.DataFrame({'股票代码': ts_codes})
        for col in self.config.INDEX_CODES.keys():
            result[col] = 0

        # 批量获取所有指数的成分股数据
        all_members = {}

        for col_name, idx_code in self.config.INDEX_CODES.items():
            try:
                # 获取该指数在指定日期前后一段时间的成分股数据
                import datetime as dt
                trade_dt = dt.datetime.strptime(trade_date, '%Y%m%d')

                # 获取当月的成分股数据
                start_date = trade_dt.replace(day=1).strftime('%Y%m%d')
                end_date = (trade_dt.replace(day=28) + dt.timedelta(days=4)).replace(day=1) - dt.timedelta(days=1)
                end_date = end_date.strftime('%Y%m%d')

                w = self.pro.index_weight(
                    index_code=idx_code,
                    start_date=start_date,
                    end_date=end_date
                )

                if w is not None and not w.empty:
                    # 筛选指定日期或最接近的日期
                    w_filtered = w[w['trade_date'] <= trade_date]
                    if not w_filtered.empty:
                        # 取最新日期的数据
                        latest_date = w_filtered['trade_date'].max()
                        latest_data = w_filtered[w_filtered['trade_date'] == latest_date]
                        members = set(latest_data['con_code'].unique())
                        all_members[col_name] = members
                        self.logger.debug(f"{col_name} 在 {latest_date} 包含 {len(members)} 只成分股")
                    else:
                        self.logger.warning(f"未找到 {col_name} 在 {trade_date} 之前的成分股数据")
                        all_members[col_name] = set()
                else:
                    self.logger.warning(f"未获取到 {col_name} 的成分股数据")
                    all_members[col_name] = set()

            except Exception as e:
                self.logger.warning(f'批量获取指数成分失败 {col_name} ({idx_code}): {e}')
                all_members[col_name] = set()

        # 标记成分股
        for col_name, members in all_members.items():
            if members:
                mask = result['股票代码'].isin(members)
                result.loc[mask, col_name] = 1
                marked_count = mask.sum()
                self.logger.debug(f"{col_name} 标记了 {marked_count} 只股票")

        return result[['股票代码'] + list(self.config.INDEX_CODES.keys())]

    # ========== 7. Tushare申万行业分类（优化版） ==========
    def get_sw_industry(self, trade_date: str, ts_codes: List[str]) -> pd.DataFrame:
        """获取新版申万行业 L1/L2/L3 名称 - 使用index_member_all接口"""
        self.logger.info("获取申万行业分类...")

        try:
            # 初始化结果DataFrame
            result = pd.DataFrame({'股票代码': ts_codes})
            result['新版申万一级行业名称'] = None
            result['新版申万二级行业名称'] = None
            result['新版申万三级行业名称'] = None

            # 分批处理股票代码，避免单次请求过多
            batch_size = 100
            for i in range(0, len(ts_codes), batch_size):
                batch_codes = ts_codes[i:i+batch_size]
                self.logger.info(f"处理申万行业分类批次 {i//batch_size + 1}/{(len(ts_codes)-1)//batch_size + 1}")

                for code in batch_codes:
                    try:
                        # 使用新的index_member_all接口获取单个股票的行业分类
                        sw_data = self.pro.index_member_all(
                            ts_code=code,
                            is_new='Y'  # 只获取最新的分类
                        )

                        if sw_data is not None and not sw_data.empty:
                            # 过滤有效期内的数据
                            valid_data = sw_data[
                                (sw_data['in_date'] <= trade_date) &
                                ((sw_data['out_date'].isna()) | (sw_data['out_date'] > trade_date))
                            ]

                            if not valid_data.empty:
                                # 取最新的一条记录
                                latest_record = valid_data.iloc[-1]

                                # 更新结果DataFrame
                                mask = result['股票代码'] == code
                                result.loc[mask, '新版申万一级行业名称'] = latest_record.get('l1_name')
                                result.loc[mask, '新版申万二级行业名称'] = latest_record.get('l2_name')
                                result.loc[mask, '新版申万三级行业名称'] = latest_record.get('l3_name')

                    except Exception as e:
                        self.logger.debug(f"获取 {code} 申万行业分类失败: {e}")
                        continue

                # 添加小延时避免API限制
                import time
                time.sleep(0.1)

            return result

        except Exception as e:
            self.logger.warning(f"获取申万行业分类失败: {e}")
            # 返回空的DataFrame
            df = pd.DataFrame({'股票代码': ts_codes})
            df['新版申万一级行业名称'] = None
            df['新版申万二级行业名称'] = None
            df['新版申万三级行业名称'] = None
            return df

    def get_sw_industry_batch(self, trade_date: str, ts_codes: List[str]) -> pd.DataFrame:
        """批量获取申万行业分类 - 备用方法"""
        self.logger.info("批量获取申万行业分类...")

        try:
            # 获取所有最新的申万行业成分数据
            all_sw_data = self.pro.index_member_all(is_new='Y')

            if all_sw_data is None or all_sw_data.empty:
                raise Exception("无法获取申万行业数据")

            # 过滤有效期内的数据
            valid_sw_data = all_sw_data[
                (all_sw_data['in_date'] <= trade_date) &
                ((all_sw_data['out_date'].isna()) | (all_sw_data['out_date'] > trade_date))
            ]

            # 只保留我们需要的股票
            target_sw_data = valid_sw_data[valid_sw_data['ts_code'].isin(ts_codes)]

            # 创建结果DataFrame
            result = pd.DataFrame({'股票代码': ts_codes})
            result['新版申万一级行业名称'] = None
            result['新版申万二级行业名称'] = None
            result['新版申万三级行业名称'] = None

            # 合并数据
            if not target_sw_data.empty:
                # 为每个股票选择最新的分类记录
                latest_sw = target_sw_data.sort_values('in_date').groupby('ts_code').tail(1)

                # 创建映射字典
                sw_mapping = latest_sw.set_index('ts_code')[['l1_name', 'l2_name', 'l3_name']].to_dict('index')

                # 填充结果
                for code in ts_codes:
                    if code in sw_mapping:
                        mask = result['股票代码'] == code
                        result.loc[mask, '新版申万一级行业名称'] = sw_mapping[code].get('l1_name')
                        result.loc[mask, '新版申万二级行业名称'] = sw_mapping[code].get('l2_name')
                        result.loc[mask, '新版申万三级行业名称'] = sw_mapping[code].get('l3_name')

            return result

        except Exception as e:
            self.logger.warning(f"批量获取申万行业分类失败: {e}")
            # 回退到逐个获取
            return self.get_sw_industry(trade_date, ts_codes)

    # ========== 8. 数据整合 ==========
    def build_comprehensive_dataset(self, trade_date: str, limit: Optional[int] = None) -> pd.DataFrame:
        """构建指定交易日的股票综合数据集"""
        if not self.validate_trade_date(trade_date):
            raise ValueError(f"无效的交易日期: {trade_date}")

        self.logger.info(f"开始构建 {trade_date} 的综合数据集")

        # 1) 获取股票列表
        base = self.get_stock_list_and_names(trade_date)
        if limit:
            base = base.head(limit)
            self.logger.info(f"限制处理前 {limit} 只股票")

        stock_list = base['ts_code'].tolist()

        # 2) 下载分钟数据（顺序处理避免序列化问题）
        self.logger.info("开始下载分钟数据...")
        download_results = []
        for i, s in enumerate(stock_list):
            if i % 10 == 0:
                self.logger.info(f"下载进度: {i}/{len(stock_list)}")
            result = self.download_minute_data(s, trade_date)
            download_results.append(result)
        success_count = sum(download_results)
        self.logger.info(f"分钟数据下载完成，成功: {success_count}/{len(stock_list)}")

        # 3) 抽取日内指标（顺序处理避免序列化问题）
        self.logger.info("开始抽取日内指标...")
        intraday_rows = []
        for i, s in enumerate(stock_list):
            if i % 10 == 0:
                self.logger.info(f"抽取进度: {i}/{len(stock_list)}")
            result = self.extract_intraday_metrics(s, trade_date)
            if result:
                intraday_rows.append(result)
        intraday = pd.DataFrame(intraday_rows)
        self.logger.info(f"成功抽取 {len(intraday)} 只股票的日内指标")

        # 4) 获取其他数据
        mv = self.get_daily_basic_mv(trade_date)
        fin = self.get_financials_full(trade_date, stock_list)
        mf = self.get_moneyflow(trade_date)

        # 指数成分股标识 - 优先使用直接方法，失败时使用批量方法
        try:
            idx_flags = self.get_index_membership_flags(trade_date, stock_list)
        except Exception as e:
            self.logger.warning(f"直接获取指数成分失败，尝试批量方法: {e}")
            try:
                idx_flags = self.get_index_membership_flags_batch(trade_date, stock_list)
            except Exception as e2:
                self.logger.error(f"批量获取指数成分也失败: {e2}")
                # 创建空的指数成分DataFrame
                idx_flags = pd.DataFrame({'股票代码': stock_list})
                for col in self.config.INDEX_CODES.keys():
                    idx_flags[col] = 0

        # 申万行业分类 - 优先使用批量方法，失败时回退到逐个获取
        try:
            sw_ind = self.get_sw_industry_batch(trade_date, stock_list)
        except Exception as e:
            self.logger.warning(f"批量获取申万行业失败，回退到逐个获取: {e}")
            sw_ind = self.get_sw_industry(trade_date, stock_list)

        # 5) 合并数据
        self.logger.info("合并所有数据...")
        result = base.rename(columns={
            'ts_code': '股票代码',
            'trade_date': '交易日期'
        })[['股票代码', '股票名称', '交易日期']]

        # 逐步合并
        merge_steps = [
            (intraday, ['股票代码', '交易日期']),
            (mv, ['股票代码']),
            (fin, ['股票代码']),
            (mf, ['股票代码']),
            (idx_flags, ['股票代码']),
            (sw_ind, ['股票代码']),
        ]

        for df_to_merge, merge_keys in merge_steps:
            if not df_to_merge.empty:
                result = result.merge(df_to_merge, on=merge_keys, how='left')

        # 填充指数成分标识的缺失值
        for col in self.config.INDEX_CODES.keys():
            if col in result.columns:
                result[col] = result[col].fillna(0).astype(int)

        # 按指定列顺序输出
        ordered_cols = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产',
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
            '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:45收盘价', '09:55收盘价'
        ]

        # 确保所有列存在
        for col in ordered_cols:
            if col not in result.columns:
                result[col] = None

        # 再次确保指数成分列为数值且缺失填0
        for col in self.config.INDEX_CODES.keys():
            if col in result.columns:
                result[col] = pd.to_numeric(result[col], errors='coerce').fillna(0).astype(int)
            else:
                result[col] = 0

        result = result[ordered_cols]

        self.logger.info(f"综合数据集构建完成，共 {len(result)} 行数据")
        return result


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='获取综合股票数据（整合QMT和Tushare）')
    parser.add_argument('--trade_date', required=True, help='交易日期，格式YYYYMMDD，如 20231108')
    parser.add_argument('--limit', type=int, default=None, help='仅处理前N只股票用于调试')
    parser.add_argument('--out', default=None, help='输出CSV路径，默认 ./综合数据_YYYYMMDD.csv')
    parser.add_argument('--log_level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    try:
        # 初始化配置和处理器
        config = Config()
        processor = ComprehensiveStockDataProcessor(config)
        processor.logger.setLevel(getattr(logging, args.log_level.upper()))

        print(f"开始处理 {args.trade_date} 的综合数据...")

        # 构建数据集
        df = processor.build_comprehensive_dataset(args.trade_date, limit=args.limit)

        # 保存结果
        out_path = args.out or f'综合数据_{args.trade_date}.csv'
        df.to_csv(out_path, index=False, encoding='utf-8-sig')

        print(f'数据已保存到: {out_path} （共 {len(df)} 行）')

    except Exception as e:
        logger = setup_logging(args.log_level)
        logger.error(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
