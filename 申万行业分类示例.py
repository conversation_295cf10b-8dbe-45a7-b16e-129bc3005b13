#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
申万行业分类使用示例
展示如何使用新的index_member_all接口获取申万行业数据
"""

import pandas as pd
import tushare as ts

# 配置Tushare
TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
pro = ts.pro_api(TOKEN)

def example_1_single_stock():
    """示例1：获取单个股票的行业分类"""
    print("=" * 50)
    print("示例1：获取单个股票的行业分类")
    print("=" * 50)
    
    stock_code = "000001.SZ"  # 平安银行
    
    try:
        # 获取股票的行业分类
        result = pro.index_member_all(ts_code=stock_code, is_new='Y')
        
        if result is not None and not result.empty:
            print(f"✓ {stock_code} 的行业分类:")
            for _, row in result.iterrows():
                print(f"  一级行业: {row['l1_name']}")
                print(f"  二级行业: {row['l2_name']}")
                print(f"  三级行业: {row['l3_name']}")
                print(f"  纳入日期: {row['in_date']}")
                print(f"  剔除日期: {row['out_date']}")
        else:
            print(f"⚠ 未找到 {stock_code} 的行业分类")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def example_2_industry_members():
    """示例2：获取特定行业的成分股"""
    print("\n" + "=" * 50)
    print("示例2：获取特定行业的成分股")
    print("=" * 50)
    
    # 黄金行业代码
    industry_code = "850531.SI"
    
    try:
        # 获取黄金行业的成分股
        result = pro.index_member_all(l3_code=industry_code, is_new='Y')
        
        if result is not None and not result.empty:
            print(f"✓ 黄金行业包含 {len(result)} 只股票:")
            print(f"行业名称: {result.iloc[0]['l3_name']}")
            print("\n成分股列表:")
            for i, (_, row) in enumerate(result.iterrows(), 1):
                print(f"  {i:2d}. {row['ts_code']} - {row['name']}")
        else:
            print(f"⚠ 未找到行业代码 {industry_code} 的成分股")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def example_3_batch_stocks():
    """示例3：批量获取多只股票的行业分类"""
    print("\n" + "=" * 50)
    print("示例3：批量获取多只股票的行业分类")
    print("=" * 50)
    
    # 测试股票列表
    stocks = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SH",  # 浦发银行
        "600036.SH",  # 招商银行
        "000858.SZ",  # 五粮液
    ]
    
    results = []
    
    for stock in stocks:
        try:
            result = pro.index_member_all(ts_code=stock, is_new='Y')
            
            if result is not None and not result.empty:
                # 取最新的记录
                latest = result.iloc[-1]
                results.append({
                    '股票代码': stock,
                    '股票名称': latest['name'],
                    '一级行业': latest['l1_name'],
                    '二级行业': latest['l2_name'],
                    '三级行业': latest['l3_name'],
                })
            else:
                results.append({
                    '股票代码': stock,
                    '股票名称': None,
                    '一级行业': None,
                    '二级行业': None,
                    '三级行业': None,
                })
        except Exception as e:
            print(f"⚠ 获取 {stock} 失败: {e}")
            results.append({
                '股票代码': stock,
                '股票名称': None,
                '一级行业': None,
                '二级行业': None,
                '三级行业': None,
            })
    
    # 转换为DataFrame并显示
    df = pd.DataFrame(results)
    print("✓ 批量获取结果:")
    print(df.to_string(index=False))
    
    # 保存结果
    df.to_csv("批量股票行业分类.csv", index=False, encoding='utf-8-sig')
    print("\n✓ 结果已保存到: 批量股票行业分类.csv")

def example_4_industry_overview():
    """示例4：获取行业概览"""
    print("\n" + "=" * 50)
    print("示例4：获取行业概览")
    print("=" * 50)
    
    try:
        # 获取所有最新的行业分类数据
        all_data = pro.index_member_all(is_new='Y')
        
        if all_data is not None and not all_data.empty:
            print(f"✓ 获取到 {len(all_data)} 条行业分类记录")
            
            # 统计各级行业数量
            l1_count = all_data['l1_name'].nunique()
            l2_count = all_data['l2_name'].nunique()
            l3_count = all_data['l3_name'].nunique()
            
            print(f"\n行业层级统计:")
            print(f"  一级行业: {l1_count} 个")
            print(f"  二级行业: {l2_count} 个")
            print(f"  三级行业: {l3_count} 个")
            
            # 显示一级行业列表
            print(f"\n一级行业列表:")
            l1_industries = sorted(all_data['l1_name'].unique())
            for i, industry in enumerate(l1_industries, 1):
                count = len(all_data[all_data['l1_name'] == industry])
                print(f"  {i:2d}. {industry} ({count} 只股票)")
            
            # 统计每个一级行业的股票数量
            industry_stats = all_data.groupby('l1_name').size().sort_values(ascending=False)
            
            print(f"\n股票数量最多的前10个一级行业:")
            for i, (industry, count) in enumerate(industry_stats.head(10).items(), 1):
                print(f"  {i:2d}. {industry}: {count} 只股票")
            
            # 保存行业统计
            industry_stats.to_csv("申万一级行业统计.csv", header=['股票数量'], encoding='utf-8-sig')
            print(f"\n✓ 行业统计已保存到: 申万一级行业统计.csv")
            
        else:
            print("⚠ 未获取到行业数据")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def example_5_filter_by_industry():
    """示例5：按行业筛选股票"""
    print("\n" + "=" * 50)
    print("示例5：按行业筛选股票")
    print("=" * 50)
    
    target_industry = "银行"  # 目标一级行业
    
    try:
        # 获取所有行业数据
        all_data = pro.index_member_all(is_new='Y')
        
        if all_data is not None and not all_data.empty:
            # 筛选银行行业的股票
            bank_stocks = all_data[all_data['l1_name'] == target_industry]
            
            if not bank_stocks.empty:
                print(f"✓ {target_industry}行业包含 {len(bank_stocks)} 只股票:")
                
                # 按二级行业分组
                l2_groups = bank_stocks.groupby('l2_name')
                
                for l2_name, group in l2_groups:
                    print(f"\n  {l2_name} ({len(group)} 只):")
                    for _, row in group.iterrows():
                        print(f"    {row['ts_code']} - {row['name']}")
                
                # 保存银行股票列表
                bank_list = bank_stocks[['ts_code', 'name', 'l1_name', 'l2_name', 'l3_name']]
                bank_list.to_csv(f"{target_industry}行业股票列表.csv", index=False, encoding='utf-8-sig')
                print(f"\n✓ {target_industry}行业股票列表已保存")
                
            else:
                print(f"⚠ 未找到 {target_industry} 行业的股票")
        else:
            print("⚠ 未获取到行业数据")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")

if __name__ == "__main__":
    print("申万行业分类使用示例")
    print("使用Tushare的index_member_all接口")
    
    # 运行所有示例
    example_1_single_stock()
    example_2_industry_members()
    example_3_batch_stocks()
    example_4_industry_overview()
    example_5_filter_by_industry()
    
    print("\n" + "=" * 50)
    print("🎉 所有示例运行完成！")
    print("=" * 50)
    print("\n生成的文件:")
    print("  - 批量股票行业分类.csv")
    print("  - 申万一级行业统计.csv")
    print("  - 银行行业股票列表.csv")
    print("\n这些示例展示了如何使用新的申万行业分类接口。")
    print("您可以根据需要修改股票代码和行业代码来获取不同的数据。")
