#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试综合股票数据获取脚本
"""

import sys
import os
import logging
from 下载日数据 import ComprehensiveStockDataProcessor, Config

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("测试综合股票数据获取功能")
    print("=" * 50)
    
    # 配置
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    # 设置日志级别
    processor.logger.setLevel(logging.INFO)
    
    # 测试日期
    trade_date = "20231108"
    
    try:
        print(f"\n1. 测试Tushare连接...")
        pro = processor.pro
        print("✓ Tushare连接成功")
        
        print(f"\n2. 测试获取股票列表...")
        stock_df = processor.get_stock_list_and_names(trade_date)
        print(f"✓ 获取到 {len(stock_df)} 只股票")
        print("前5只股票:")
        print(stock_df.head())
        
        # 选择前5只股票进行测试
        test_stocks = stock_df['ts_code'].head(5).tolist()
        print(f"\n3. 测试股票: {test_stocks}")
        
        print(f"\n4. 测试市值数据获取...")
        mv_df = processor.get_daily_basic_mv(trade_date)
        print(f"✓ 获取到 {len(mv_df)} 条市值数据")
        if not mv_df.empty:
            print("市值数据样例:")
            print(mv_df.head())
        
        print(f"\n5. 测试资金流向数据获取...")
        mf_df = processor.get_moneyflow(trade_date)
        print(f"✓ 获取到 {len(mf_df)} 条资金流向数据")
        if not mf_df.empty:
            print("资金流向数据样例:")
            print(mf_df.head())
        
        print(f"\n6. 测试指数成分股标识...")
        idx_df = processor.get_index_membership_flags(trade_date, test_stocks)
        print(f"✓ 获取到 {len(idx_df)} 条指数成分数据")
        print("指数成分数据样例:")
        print(idx_df.head())
        
        print(f"\n7. 测试申万行业分类...")
        sw_df = processor.get_sw_industry(trade_date, test_stocks)
        print(f"✓ 获取到 {len(sw_df)} 条行业分类数据")
        print("行业分类数据样例:")
        print(sw_df.head())
        
        print(f"\n8. 测试财务数据获取（仅测试前2只股票）...")
        fin_df = processor.get_financials_full(trade_date, test_stocks[:2])
        print(f"✓ 获取到 {len(fin_df)} 条财务数据")
        print("财务数据样例:")
        print(fin_df.head())
        
        print(f"\n9. 测试QMT分钟数据下载（仅测试第1只股票）...")
        if test_stocks:
            test_stock = test_stocks[0]
            success = processor.download_minute_data(test_stock, trade_date)
            if success:
                print(f"✓ {test_stock} 分钟数据下载成功")
                
                # 测试日内指标提取
                metrics = processor.extract_intraday_metrics(test_stock, trade_date)
                if metrics:
                    print("✓ 日内指标提取成功")
                    print("日内指标样例:")
                    for key, value in list(metrics.items())[:10]:  # 只显示前10个字段
                        print(f"  {key}: {value}")
                else:
                    print("⚠ 日内指标提取失败（可能是数据不存在）")
            else:
                print(f"⚠ {test_stock} 分钟数据下载失败")
        
        print(f"\n10. 测试完整数据集构建（仅前3只股票）...")
        df = processor.build_comprehensive_dataset(trade_date, limit=3)
        print(f"✓ 构建完成，共 {len(df)} 行数据")
        print(f"✓ 包含 {len(df.columns)} 个字段")
        
        print("\n字段列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        print("\n数据样例（前3行，前10列）:")
        print(df.iloc[:3, :10])
        
        # 保存测试结果
        output_file = f"测试结果_{trade_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 测试结果已保存到: {output_file}")
        
        print("\n" + "=" * 50)
        print("✓ 所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_with_sample_date():
    """使用示例日期进行测试"""
    print("\n" + "=" * 50)
    print("使用示例日期进行完整测试")
    print("=" * 50)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    # 使用一个较新的交易日期
    trade_date = "20241108"  # 可以根据需要调整
    
    try:
        print(f"构建 {trade_date} 的完整数据集（限制前10只股票）...")
        df = processor.build_comprehensive_dataset(trade_date, limit=10)
        
        output_file = f"完整数据_{trade_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 完整数据集构建成功")
        print(f"✓ 共 {len(df)} 行数据，{len(df.columns)} 个字段")
        print(f"✓ 结果已保存到: {output_file}")
        
        # 显示数据质量统计
        print(f"\n数据质量统计:")
        print(f"  - 股票代码: {df['股票代码'].notna().sum()}/{len(df)}")
        print(f"  - 股票名称: {df['股票名称'].notna().sum()}/{len(df)}")
        print(f"  - 开盘价: {df['开盘价'].notna().sum()}/{len(df)}")
        print(f"  - 收盘价: {df['收盘价'].notna().sum()}/{len(df)}")
        print(f"  - 流通市值: {df['流通市值'].notna().sum()}/{len(df)}")
        print(f"  - 资金流向数据: {df['散户资金买入额'].notna().sum()}/{len(df)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试综合股票数据获取功能...")
    
    # 基本功能测试
    success1 = test_basic_functionality()
    
    if success1:
        # 完整功能测试
        success2 = test_with_sample_date()
        
        if success1 and success2:
            print("\n🎉 所有测试通过！脚本可以正常使用。")
        else:
            print("\n⚠ 部分测试失败，请检查配置和网络连接。")
    else:
        print("\n❌ 基本测试失败，请检查环境配置。")
