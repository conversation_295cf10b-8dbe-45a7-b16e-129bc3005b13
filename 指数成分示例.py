#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指数成分和权重使用示例
展示如何使用index_weight接口获取指数成分数据
"""

import pandas as pd
import tushare as ts
import datetime as dt

# 配置Tushare
TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
pro = ts.pro_api(TOKEN)

# 主要指数代码
INDEX_CODES = {
    '沪深300': '399300.SZ',
    '上证50': '000016.SH', 
    '中证500': '000905.SH',
    '中证1000': '000852.SH',
    '中证2000': '932000.CSI',
    '创业板指': '399006.SZ',
}

def example_1_single_index():
    """示例1：获取单个指数的成分股和权重"""
    print("=" * 50)
    print("示例1：获取单个指数的成分股和权重")
    print("=" * 50)
    
    index_code = "399300.SZ"  # 沪深300
    trade_date = "20231108"
    
    try:
        # 获取指数成分和权重
        result = pro.index_weight(
            index_code=index_code,
            trade_date=trade_date
        )
        
        if result is not None and not result.empty:
            print(f"✓ 沪深300指数在 {trade_date} 包含 {len(result)} 只成分股")
            
            # 基本统计
            print(f"\n权重统计:")
            print(f"  最大权重: {result['weight'].max():.4f}%")
            print(f"  最小权重: {result['weight'].min():.4f}%")
            print(f"  平均权重: {result['weight'].mean():.4f}%")
            print(f"  权重总和: {result['weight'].sum():.2f}%")
            
            # 前10大权重股票
            print(f"\n前10大权重股票:")
            top10 = result.nlargest(10, 'weight')
            for i, (_, row) in enumerate(top10.iterrows(), 1):
                print(f"  {i:2d}. {row['con_code']}: {row['weight']:.4f}%")
                
        else:
            print(f"⚠ 未找到沪深300指数在 {trade_date} 的成分股数据")
            
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def example_2_multiple_indices():
    """示例2：获取多个指数的成分股数量"""
    print("\n" + "=" * 50)
    print("示例2：获取多个指数的成分股数量")
    print("=" * 50)
    
    trade_date = "20231108"
    results = []
    
    for index_name, index_code in INDEX_CODES.items():
        try:
            # 获取指数成分
            w = pro.index_weight(
                index_code=index_code,
                trade_date=trade_date
            )
            
            if w is not None and not w.empty:
                results.append({
                    '指数名称': index_name,
                    '指数代码': index_code,
                    '成分股数量': len(w),
                    '权重总和': w['weight'].sum(),
                    '最大权重': w['weight'].max(),
                    '最小权重': w['weight'].min(),
                })
            else:
                results.append({
                    '指数名称': index_name,
                    '指数代码': index_code,
                    '成分股数量': 0,
                    '权重总和': 0,
                    '最大权重': 0,
                    '最小权重': 0,
                })
                print(f"⚠ 未获取到 {index_name} 的数据")
                
        except Exception as e:
            print(f"⚠ 获取 {index_name} 失败: {e}")
            results.append({
                '指数名称': index_name,
                '指数代码': index_code,
                '成分股数量': 0,
                '权重总和': 0,
                '最大权重': 0,
                '最小权重': 0,
            })
    
    # 转换为DataFrame并显示
    df = pd.DataFrame(results)
    print("✓ 多指数成分股统计:")
    print(df.to_string(index=False, float_format='%.4f'))
    
    # 保存结果
    df.to_csv("多指数成分统计.csv", index=False, encoding='utf-8-sig')
    print("\n✓ 结果已保存到: 多指数成分统计.csv")

def example_3_index_overlap():
    """示例3：分析指数成分股重叠情况"""
    print("\n" + "=" * 50)
    print("示例3：分析指数成分股重叠情况")
    print("=" * 50)
    
    trade_date = "20231108"
    all_members = {}
    
    # 获取所有指数的成分股
    for index_name, index_code in INDEX_CODES.items():
        try:
            w = pro.index_weight(
                index_code=index_code,
                trade_date=trade_date
            )
            
            if w is not None and not w.empty:
                all_members[index_name] = set(w['con_code'].tolist())
                print(f"✓ {index_name}: {len(all_members[index_name])} 只成分股")
            else:
                all_members[index_name] = set()
                print(f"⚠ {index_name}: 未获取到数据")
                
        except Exception as e:
            print(f"⚠ 获取 {index_name} 失败: {e}")
            all_members[index_name] = set()
    
    # 分析重叠情况
    print(f"\n指数重叠分析:")
    
    # 沪深300 vs 其他指数
    hs300_members = all_members.get('沪深300', set())
    if hs300_members:
        for index_name, members in all_members.items():
            if index_name != '沪深300' and members:
                overlap = hs300_members & members
                overlap_ratio = len(overlap) / len(members) * 100 if members else 0
                print(f"  沪深300 与 {index_name}: {len(overlap)} 只重叠 ({overlap_ratio:.1f}%)")
    
    # 找出属于多个指数的股票
    all_stocks = set()
    for members in all_members.values():
        all_stocks.update(members)
    
    multi_index_stocks = []
    for stock in all_stocks:
        indices = [name for name, members in all_members.items() if stock in members]
        if len(indices) > 1:
            multi_index_stocks.append({
                '股票代码': stock,
                '所属指数': ', '.join(indices),
                '指数数量': len(indices)
            })
    
    if multi_index_stocks:
        multi_df = pd.DataFrame(multi_index_stocks)
        multi_df = multi_df.sort_values('指数数量', ascending=False)
        
        print(f"\n属于多个指数的股票 (前20只):")
        print(multi_df.head(20).to_string(index=False))
        
        # 保存结果
        multi_df.to_csv("指数重叠分析.csv", index=False, encoding='utf-8-sig')
        print(f"\n✓ 重叠分析结果已保存到: 指数重叠分析.csv")

def example_4_historical_changes():
    """示例4：分析指数成分的历史变化"""
    print("\n" + "=" * 50)
    print("示例4：分析指数成分的历史变化")
    print("=" * 50)
    
    index_code = "399300.SZ"  # 沪深300
    
    # 获取最近3个月的数据
    end_date = dt.datetime.strptime("20231108", '%Y%m%d')
    start_date = end_date - dt.timedelta(days=90)
    
    try:
        # 获取历史成分数据
        w = pro.index_weight(
            index_code=index_code,
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )
        
        if w is not None and not w.empty:
            print(f"✓ 获取到 {len(w)} 条历史记录")
            
            # 按日期分组
            dates = sorted(w['trade_date'].unique())
            print(f"  数据日期范围: {dates[0]} - {dates[-1]}")
            print(f"  包含 {len(dates)} 个交易日")
            
            # 分析成分股数量变化
            daily_counts = w.groupby('trade_date').size()
            print(f"\n成分股数量变化:")
            print(f"  最多: {daily_counts.max()} 只")
            print(f"  最少: {daily_counts.min()} 只")
            print(f"  平均: {daily_counts.mean():.1f} 只")
            
            # 找出新增和剔除的股票
            if len(dates) >= 2:
                first_date = dates[0]
                last_date = dates[-1]
                
                first_members = set(w[w['trade_date'] == first_date]['con_code'])
                last_members = set(w[w['trade_date'] == last_date]['con_code'])
                
                added = last_members - first_members
                removed = first_members - last_members
                
                print(f"\n成分股变化 ({first_date} -> {last_date}):")
                print(f"  新增: {len(added)} 只")
                if added:
                    print(f"    {', '.join(list(added)[:10])}{'...' if len(added) > 10 else ''}")
                
                print(f"  剔除: {len(removed)} 只")
                if removed:
                    print(f"    {', '.join(list(removed)[:10])}{'...' if len(removed) > 10 else ''}")
            
            # 保存历史数据
            w.to_csv("沪深300历史成分.csv", index=False, encoding='utf-8-sig')
            print(f"\n✓ 历史数据已保存到: 沪深300历史成分.csv")
            
        else:
            print(f"⚠ 未获取到历史数据")
            
    except Exception as e:
        print(f"❌ 获取历史数据失败: {e}")

def example_5_weight_analysis():
    """示例5：权重分布分析"""
    print("\n" + "=" * 50)
    print("示例5：权重分布分析")
    print("=" * 50)
    
    index_code = "399300.SZ"  # 沪深300
    trade_date = "20231108"
    
    try:
        # 获取成分股权重
        w = pro.index_weight(
            index_code=index_code,
            trade_date=trade_date
        )
        
        if w is not None and not w.empty:
            print(f"✓ 沪深300指数权重分析 ({trade_date})")
            
            # 权重分布区间
            bins = [0, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, float('inf')]
            labels = ['<0.1%', '0.1-0.2%', '0.2-0.5%', '0.5-1.0%', '1.0-2.0%', '2.0-5.0%', '>5.0%']
            
            w['weight_range'] = pd.cut(w['weight'], bins=bins, labels=labels, right=False)
            weight_dist = w['weight_range'].value_counts().sort_index()
            
            print(f"\n权重分布:")
            for range_label, count in weight_dist.items():
                percentage = count / len(w) * 100
                weight_sum = w[w['weight_range'] == range_label]['weight'].sum()
                print(f"  {range_label}: {count:3d} 只 ({percentage:5.1f}%) - 权重合计: {weight_sum:6.2f}%")
            
            # 集中度分析
            w_sorted = w.sort_values('weight', ascending=False)
            
            print(f"\n集中度分析:")
            for n in [10, 20, 50, 100]:
                if len(w_sorted) >= n:
                    top_n_weight = w_sorted.head(n)['weight'].sum()
                    print(f"  前{n:3d}大权重股占比: {top_n_weight:6.2f}%")
            
            # 权重极值分析
            print(f"\n权重极值:")
            print(f"  最大权重股票: {w_sorted.iloc[0]['con_code']} ({w_sorted.iloc[0]['weight']:.4f}%)")
            print(f"  最小权重股票: {w_sorted.iloc[-1]['con_code']} ({w_sorted.iloc[-1]['weight']:.4f}%)")
            print(f"  权重比值: {w_sorted.iloc[0]['weight'] / w_sorted.iloc[-1]['weight']:.1f}:1")
            
            # 保存权重分析
            w_analysis = w.copy()
            w_analysis = w_analysis.sort_values('weight', ascending=False)
            w_analysis['rank'] = range(1, len(w_analysis) + 1)
            w_analysis['cumulative_weight'] = w_analysis['weight'].cumsum()
            
            w_analysis.to_csv("沪深300权重分析.csv", index=False, encoding='utf-8-sig')
            print(f"\n✓ 权重分析结果已保存到: 沪深300权重分析.csv")
            
        else:
            print(f"⚠ 未获取到权重数据")
            
    except Exception as e:
        print(f"❌ 权重分析失败: {e}")

if __name__ == "__main__":
    print("指数成分和权重使用示例")
    print("使用Tushare的index_weight接口")
    
    # 运行所有示例
    example_1_single_index()
    example_2_multiple_indices()
    example_3_index_overlap()
    example_4_historical_changes()
    example_5_weight_analysis()
    
    print("\n" + "=" * 50)
    print("🎉 所有示例运行完成！")
    print("=" * 50)
    print("\n生成的文件:")
    print("  - 多指数成分统计.csv")
    print("  - 指数重叠分析.csv")
    print("  - 沪深300历史成分.csv")
    print("  - 沪深300权重分析.csv")
    print("\n这些示例展示了如何使用index_weight接口进行指数分析。")
    print("您可以根据需要修改指数代码和日期来获取不同的数据。")
