#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合股票数据获取系统 - 完整测试套件
测试所有36个字段的数据获取功能，包括优化后的申万行业和指数成分功能
"""

import os
import sys
import logging
import datetime as dt
import pandas as pd
from 下载日数据 import ComprehensiveStockDataProcessor, Config

class ComprehensiveTestSuite:
    """综合测试套件"""
    
    def __init__(self):
        self.config = Config()
        self.processor = ComprehensiveStockDataProcessor(self.config)
        self.logger = self.setup_logger()
        self.test_results = {}
        
        # 测试参数
        self.trade_date = "20231108"
        self.test_stocks = [
            "000001.SZ",  # 平安银行
            "000002.SZ",  # 万科A
            "600000.SH",  # 浦发银行
            "600036.SH",  # 招商银行
            "600519.SH",  # 贵州茅台
            "000858.SZ",  # 五粮液
            "600276.SH",  # 恒瑞医药
            "000725.SZ",  # 京东方A
            "002415.SZ",  # 海康威视
            "300059.SZ",  # 东方财富
        ]
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('ComprehensiveTest')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def test_connection(self):
        """测试连接"""
        print("=" * 80)
        print("1. 连接测试")
        print("=" * 80)
        
        try:
            # 测试Tushare连接
            pro = self.processor.pro
            self.logger.info("✓ Tushare连接成功")
            
            # 测试QMT连接（通过获取交易日历）
            try:
                import xtquant.xtdata as xtdata
                # 简单测试QMT连接
                self.logger.info("✓ QMT连接测试通过")
            except Exception as e:
                self.logger.warning(f"⚠ QMT连接测试失败: {e}")
            
            self.test_results['connection'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 连接测试失败: {e}")
            self.test_results['connection'] = False
            return False
    
    def test_basic_data(self):
        """测试基础数据获取"""
        print("\n" + "=" * 80)
        print("2. 基础数据测试")
        print("=" * 80)
        
        try:
            # 测试股票列表获取
            self.logger.info("测试股票列表获取...")
            stock_df = self.processor.get_stock_list_and_names(self.trade_date)
            self.logger.info(f"✓ 获取到 {len(stock_df)} 只股票")
            
            # 测试市值数据
            self.logger.info("测试市值数据获取...")
            mv_df = self.processor.get_daily_basic_mv(self.trade_date)
            self.logger.info(f"✓ 获取到 {len(mv_df)} 条市值数据")
            
            # 测试资金流向数据
            self.logger.info("测试资金流向数据获取...")
            mf_df = self.processor.get_moneyflow(self.trade_date)
            self.logger.info(f"✓ 获取到 {len(mf_df)} 条资金流向数据")
            
            self.test_results['basic_data'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 基础数据测试失败: {e}")
            self.test_results['basic_data'] = False
            return False
    
    def test_financial_data(self):
        """测试财务数据获取"""
        print("\n" + "=" * 80)
        print("3. 财务数据测试")
        print("=" * 80)
        
        try:
            self.logger.info("测试财务数据获取（前5只股票）...")
            fin_df = self.processor.get_financials_full(self.trade_date, self.test_stocks[:5])
            self.logger.info(f"✓ 获取到 {len(fin_df)} 条财务数据")
            
            # 检查关键字段
            key_fields = ['净利润TTM', '现金流TTM', '净资产', '总资产', '总负债', '净利润(当季)']
            for field in key_fields:
                not_null_count = fin_df[field].notna().sum()
                self.logger.info(f"  {field}: {not_null_count}/{len(fin_df)} 有数据")
            
            self.test_results['financial_data'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 财务数据测试失败: {e}")
            self.test_results['financial_data'] = False
            return False
    
    def test_index_membership(self):
        """测试指数成分股标识（优化版）"""
        print("\n" + "=" * 80)
        print("4. 指数成分股测试（优化版）")
        print("=" * 80)
        
        try:
            self.logger.info("测试指数成分股标识...")
            idx_df = self.processor.get_index_membership_flags(self.trade_date, self.test_stocks)
            self.logger.info(f"✓ 获取到 {len(idx_df)} 条指数成分数据")
            
            # 检查各指数成分数量
            for col in self.config.INDEX_CODES.keys():
                member_count = idx_df[col].sum()
                self.logger.info(f"  {col}: {member_count} 只成分股")
            
            self.test_results['index_membership'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 指数成分股测试失败: {e}")
            self.test_results['index_membership'] = False
            return False
    
    def test_sw_industry(self):
        """测试申万行业分类（优化版）"""
        print("\n" + "=" * 80)
        print("5. 申万行业分类测试（优化版）")
        print("=" * 80)
        
        try:
            self.logger.info("测试申万行业分类...")
            sw_df = self.processor.get_sw_industry_batch(self.trade_date, self.test_stocks)
            self.logger.info(f"✓ 获取到 {len(sw_df)} 条行业分类数据")
            
            # 检查行业分类完整性
            sw_fields = ['新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称']
            for field in sw_fields:
                not_null_count = sw_df[field].notna().sum()
                self.logger.info(f"  {field}: {not_null_count}/{len(sw_df)} 有数据")
            
            self.test_results['sw_industry'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 申万行业分类测试失败: {e}")
            self.test_results['sw_industry'] = False
            return False
    
    def test_qmt_data(self):
        """测试QMT数据获取"""
        print("\n" + "=" * 80)
        print("6. QMT数据测试")
        print("=" * 80)
        
        try:
            # 测试单只股票的分钟数据
            test_stock = self.test_stocks[0]
            self.logger.info(f"测试 {test_stock} 的分钟数据下载...")
            
            success = self.processor.download_minute_data(test_stock, self.trade_date)
            if success:
                self.logger.info(f"✓ {test_stock} 分钟数据下载成功")
                
                # 测试日内指标提取
                metrics = self.processor.extract_intraday_metrics(test_stock, self.trade_date)
                if metrics:
                    self.logger.info("✓ 日内指标提取成功")
                    
                    # 检查关键字段
                    key_fields = ['开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额']
                    for field in key_fields:
                        if field in metrics and metrics[field] is not None:
                            self.logger.info(f"  {field}: {metrics[field]}")
                    
                    # 检查特定时间点价格
                    time_fields = ['09:35收盘价', '09:45收盘价', '09:55收盘价']
                    for field in time_fields:
                        if field in metrics:
                            self.logger.info(f"  {field}: {metrics[field]}")
                else:
                    self.logger.warning("⚠ 日内指标提取失败（可能是数据不存在）")
            else:
                self.logger.warning(f"⚠ {test_stock} 分钟数据下载失败")
            
            self.test_results['qmt_data'] = success
            return success
            
        except Exception as e:
            self.logger.error(f"❌ QMT数据测试失败: {e}")
            self.test_results['qmt_data'] = False
            return False
    
    def test_comprehensive_dataset(self):
        """测试完整数据集构建"""
        print("\n" + "=" * 80)
        print("7. 完整数据集测试")
        print("=" * 80)
        
        try:
            self.logger.info("构建完整数据集（限制前10只股票）...")
            df = self.processor.build_comprehensive_dataset(self.trade_date, limit=10)
            
            self.logger.info(f"✓ 数据集构建成功，共 {len(df)} 行数据，{len(df.columns)} 个字段")
            
            # 验证所有36个字段
            expected_fields = [
                '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价', 
                '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', 
                '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
                '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
                '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
                '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
                '09:35收盘价', '09:45收盘价', '09:55收盘价'
            ]
            
            missing_fields = [field for field in expected_fields if field not in df.columns]
            if missing_fields:
                self.logger.warning(f"⚠ 缺失字段: {missing_fields}")
            else:
                self.logger.info("✓ 所有36个字段都存在")
            
            # 数据质量统计
            self.logger.info("\n数据质量统计:")
            quality_fields = [
                '股票代码', '股票名称', '开盘价', '收盘价', '流通市值', 
                '散户资金买入额', '沪深300成分股', '新版申万一级行业名称'
            ]
            
            for field in quality_fields:
                if field in df.columns:
                    not_null_count = df[field].notna().sum()
                    percentage = not_null_count / len(df) * 100
                    self.logger.info(f"  {field}: {not_null_count}/{len(df)} ({percentage:.1f}%)")
            
            # 保存测试结果
            output_file = f"综合测试结果_{self.trade_date}.csv"
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            self.logger.info(f"✓ 测试结果已保存到: {output_file}")
            
            self.test_results['comprehensive_dataset'] = True
            self.test_results['field_count'] = len(df.columns)
            self.test_results['missing_fields'] = missing_fields
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 完整数据集测试失败: {e}")
            self.test_results['comprehensive_dataset'] = False
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始综合测试套件")
        print(f"测试日期: {self.trade_date}")
        print(f"测试股票: {len(self.test_stocks)} 只")
        
        start_time = dt.datetime.now()
        
        # 运行所有测试
        tests = [
            self.test_connection,
            self.test_basic_data,
            self.test_financial_data,
            self.test_index_membership,
            self.test_sw_industry,
            self.test_qmt_data,
            self.test_comprehensive_dataset,
        ]
        
        passed_tests = 0
        for test in tests:
            try:
                if test():
                    passed_tests += 1
            except Exception as e:
                self.logger.error(f"测试执行异常: {e}")
        
        end_time = dt.datetime.now()
        duration = end_time - start_time
        
        # 输出测试总结
        self.print_summary(passed_tests, len(tests), duration)
        
        return passed_tests == len(tests)
    
    def print_summary(self, passed, total, duration):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("🎯 测试总结")
        print("=" * 80)
        
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        print(f"耗时: {duration}")
        
        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✓" if result else "❌"
            print(f"  {status} {test_name}")
        
        if passed == total:
            print(f"\n🎉 所有测试通过！系统运行正常。")
        else:
            print(f"\n⚠ 部分测试失败，请检查配置和网络连接。")
        
        # 特殊提示
        if 'field_count' in self.test_results:
            print(f"\n📊 数据字段统计:")
            print(f"  实际字段数: {self.test_results['field_count']}")
            print(f"  期望字段数: 36")
            
            if self.test_results.get('missing_fields'):
                print(f"  缺失字段: {self.test_results['missing_fields']}")

if __name__ == "__main__":
    # 运行综合测试
    test_suite = ComprehensiveTestSuite()
    success = test_suite.run_all_tests()
    
    sys.exit(0 if success else 1)
