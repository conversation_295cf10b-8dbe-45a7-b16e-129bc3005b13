# 综合股票数据获取脚本使用说明

## 概述

本脚本整合了QMT和Tushare两个数据源，能够获取包含36个字段的完整股票数据集，包括：

- 基础行情数据（开盘价、收盘价、成交量等）
- 财务数据（净利润、现金流、资产负债等）
- 资金流向数据（散户、中户、大户、机构资金流向）
- 指数成分股标识（沪深300、上证50、中证500等）
- 申万行业分类（一级、二级、三级）
- 特定时间点价格（09:35、09:45、09:55收盘价）

## 数据字段列表

| 序号 | 字段名称 | 数据源 | 说明 |
|------|----------|--------|------|
| 1 | 股票代码 | - | TS代码格式，如000001.SZ |
| 2 | 股票名称 | Tushare | 股票简称 |
| 3 | 交易日期 | - | YYYYMMDD格式 |
| 4 | 开盘价 | QMT | 当日开盘价 |
| 5 | 最高价 | QMT | 当日最高价 |
| 6 | 最低价 | QMT | 当日最低价 |
| 7 | 收盘价 | QMT | 当日收盘价 |
| 8 | 前收盘价 | QMT | 前一交易日收盘价 |
| 9 | 成交量 | QMT | 当日成交量（股） |
| 10 | 成交额 | QMT | 当日成交额（元） |
| 11 | 流通市值 | Tushare | 流通市值（万元） |
| 12 | 总市值 | Tushare | 总市值（万元） |
| 13 | 净利润TTM | Tushare | 最近4个季度净利润合计 |
| 14 | 现金流TTM | Tushare | 最近4个季度经营现金流合计 |
| 15 | 净资产 | Tushare | 最新期净资产 |
| 16 | 总资产 | Tushare | 最新期总资产 |
| 17 | 总负债 | Tushare | 最新期总负债 |
| 18 | 净利润(当季) | Tushare | 最新季度净利润 |
| 19 | 中户资金买入额 | Tushare | 中户资金买入金额 |
| 20 | 中户资金卖出额 | Tushare | 中户资金卖出金额 |
| 21 | 大户资金买入额 | Tushare | 大户资金买入金额 |
| 22 | 大户资金卖出额 | Tushare | 大户资金卖出金额 |
| 23 | 散户资金买入额 | Tushare | 散户资金买入金额 |
| 24 | 散户资金卖出额 | Tushare | 散户资金卖出金额 |
| 25 | 机构资金买入额 | Tushare | 机构资金买入金额 |
| 26 | 机构资金卖出额 | Tushare | 机构资金卖出金额 |
| 27 | 沪深300成分股 | Tushare | 是否为沪深300成分股（0/1） |
| 28 | 上证50成分股 | Tushare | 是否为上证50成分股（0/1） |
| 29 | 中证500成分股 | Tushare | 是否为中证500成分股（0/1） |
| 30 | 中证1000成分股 | Tushare | 是否为中证1000成分股（0/1） |
| 31 | 中证2000成分股 | Tushare | 是否为中证2000成分股（0/1） |
| 32 | 创业板指成分股 | Tushare | 是否为创业板指成分股（0/1） |
| 33 | 新版申万一级行业名称 | Tushare | 申万一级行业分类 |
| 34 | 新版申万二级行业名称 | Tushare | 申万二级行业分类 |
| 35 | 新版申万三级行业名称 | Tushare | 申万三级行业分类 |
| 36 | 09:35收盘价 | QMT | 09:35时点的价格 |
| 37 | 09:45收盘价 | QMT | 09:45时点的价格 |
| 38 | 09:55收盘价 | QMT | 09:55时点的价格 |

## 环境要求

### 软件环境
- Python 3.6+
- MiniQMT客户端（需要启动）
- 网络连接（访问Tushare API）

### Python依赖包
```bash
pip install pandas tushare xtquant
```

### 数据权限
- Tushare积分：建议2000+（用于获取资金流向等高级数据）
- QMT账户：需要有行情权限

## 使用方法

### 1. 命令行使用

```bash
# 获取指定日期的完整数据
python 下载日数据.py --trade_date 20231108

# 限制股票数量（用于测试）
python 下载日数据.py --trade_date 20231108 --limit 10

# 指定输出文件
python 下载日数据.py --trade_date 20231108 --out my_data.csv

# 设置日志级别
python 下载日数据.py --trade_date 20231108 --log_level DEBUG
```

### 2. 代码中使用

```python
from 下载日数据 import ComprehensiveStockDataProcessor, Config

# 创建处理器
config = Config()
processor = ComprehensiveStockDataProcessor(config)

# 获取数据
trade_date = "20231108"
df = processor.build_comprehensive_dataset(trade_date, limit=100)

# 保存数据
df.to_csv(f"股票数据_{trade_date}.csv", index=False, encoding='utf-8-sig')
```

### 3. 测试脚本

```bash
# 运行测试脚本
python 测试综合数据获取.py
```

## 配置说明

可以通过修改`Config`类来调整参数：

```python
@dataclass
class Config:
    # Tushare Token（必须配置）
    TUSHARE_TOKEN: str = "your_token_here"
    
    # 复权方式
    DIVIDEND_TYPE: str = 'front'  # front: 前复权; none: 不复权
    
    # K线周期
    PERIOD: str = '5m'  # 5分钟K线
    
    # 并发数
    N_JOBS: int = 4
    
    # 批处理大小
    BATCH_SIZE: int = 50
    
    # 重试次数
    MAX_RETRIES: int = 3
```

## 注意事项

1. **MiniQMT启动**：使用前必须启动MiniQMT客户端
2. **Tushare Token**：需要配置有效的Tushare Token
3. **数据权限**：某些高级数据需要足够的Tushare积分
4. **网络连接**：需要稳定的网络连接访问Tushare API
5. **数据完整性**：部分字段可能因为权限或数据缺失而为空
6. **处理时间**：完整数据获取可能需要较长时间，建议先用小样本测试

## 错误处理

脚本包含完善的错误处理机制：
- 网络超时自动重试
- 数据缺失时填充默认值
- 详细的日志记录
- 异常情况下的优雅降级

## 输出格式

输出为CSV文件，包含以下特点：
- UTF-8编码（带BOM，Excel兼容）
- 列名为中文
- 数值型数据保持原始精度
- 缺失值显示为空

## 性能优化建议

1. **限制股票数量**：测试时使用`--limit`参数
2. **调整并发数**：根据网络情况调整`N_JOBS`
3. **分批处理**：大量数据可分多次获取
4. **缓存利用**：QMT数据会缓存，重复获取更快

## 常见问题

### Q: 提示"未配置 Tushare Token"
A: 需要在代码中设置有效的Tushare Token，或设置环境变量`TUSHARE_TOKEN`

### Q: QMT数据获取失败
A: 确保MiniQMT客户端已启动且网络连接正常

### Q: 部分字段为空
A: 可能是数据权限不足或该股票确实缺少相关数据

### Q: 处理速度慢
A: 可以调整并发数或使用`--limit`参数限制处理的股票数量

## 技术支持

如有问题，请检查：
1. 环境配置是否正确
2. 网络连接是否稳定
3. 数据权限是否充足
4. 日志输出中的错误信息
