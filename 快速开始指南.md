# 综合股票数据获取系统 - 快速开始指南

## 🚀 5分钟快速上手

### 第一步：环境准备

1. **安装依赖**
```bash
pip install pandas tushare xtquant
```

2. **启动MiniQMT客户端**
   - 下载并安装MiniQMT
   - 启动客户端并登录

3. **配置Tushare Token**
   - 注册Tushare账号：https://tushare.pro
   - 获取Token（建议2000积分以上）
   - 在脚本中配置Token

### 第二步：快速测试

```bash
# 运行综合测试，验证所有功能
python 综合测试套件.py
```

### 第三步：获取数据

```bash
# 获取指定日期的完整数据（测试用）
python 下载日数据.py --trade_date 20231108 --limit 10

# 获取完整数据
python 下载日数据.py --trade_date 20231108
```

## 📊 输出数据说明

### 36个字段分类

#### 基础信息（3个）
- 股票代码、股票名称、交易日期

#### QMT行情数据（9个）
- 开盘价、最高价、最低价、收盘价、前收盘价
- 成交量、成交额
- 09:35收盘价、09:45收盘价、09:55收盘价

#### Tushare市值数据（2个）
- 流通市值、总市值

#### 财务数据（6个）
- 净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)

#### 资金流向（8个）
- 中户资金买入额、中户资金卖出额
- 大户资金买入额、大户资金卖出额
- 散户资金买入额、散户资金卖出额
- 机构资金买入额、机构资金卖出额

#### 指数成分（6个）
- 沪深300成分股、上证50成分股、中证500成分股
- 中证1000成分股、中证2000成分股、创业板指成分股

#### 申万行业（3个）
- 新版申万一级行业名称、新版申万二级行业名称、新版申万三级行业名称

## 🔧 常用命令

### 基本使用
```bash
# 获取指定日期数据
python 下载日数据.py --trade_date 20231108

# 限制股票数量（调试用）
python 下载日数据.py --trade_date 20231108 --limit 50

# 指定输出文件
python 下载日数据.py --trade_date 20231108 --out 我的数据.csv

# 设置日志级别
python 下载日数据.py --trade_date 20231108 --log_level DEBUG
```

### 专项测试
```bash
# 测试申万行业分类
python 测试申万行业分类.py

# 测试指数成分
python 测试指数成分.py

# 查看申万行业示例
python 申万行业分类示例.py

# 查看指数成分示例
python 指数成分示例.py
```

## 📈 数据质量预期

### 高质量字段（>95%）
- 股票代码、股票名称、交易日期
- 基础行情数据（开盘价、收盘价等）
- 流通市值、总市值

### 中等质量字段（70-95%）
- 资金流向数据
- 指数成分股标识
- 申万行业分类

### 可能缺失字段（<70%）
- 财务数据（部分股票可能缺失）
- 特定时间点价格（停牌股票）

## ⚠️ 注意事项

### 必要条件
1. **MiniQMT客户端**：必须启动并登录
2. **Tushare积分**：建议2000积分以上
3. **网络连接**：稳定的网络环境
4. **交易日期**：使用有效的交易日期

### 性能建议
1. **首次使用**：先用`--limit 10`测试
2. **大批量数据**：分多次获取，避免超时
3. **网络优化**：避开交易时间高峰期
4. **错误处理**：查看日志输出定位问题

## 🎯 使用场景

### 量化研究
```python
from 下载日数据 import ComprehensiveStockDataProcessor

processor = ComprehensiveStockDataProcessor()
df = processor.build_comprehensive_dataset("20231108")

# 筛选沪深300成分股
hs300_stocks = df[df['沪深300成分股'] == 1]

# 按行业分析
industry_analysis = df.groupby('新版申万一级行业名称').agg({
    '流通市值': 'sum',
    '成交额': 'sum'
})
```

### 数据分析
```python
# 资金流向分析
df['净流入'] = (df['大户资金买入额'] + df['机构资金买入额']) - \
              (df['大户资金卖出额'] + df['机构资金卖出额'])

# 估值分析
df['市净率'] = df['总市值'] / df['净资产']
```

### 投资筛选
```python
# 筛选条件
conditions = (
    (df['沪深300成分股'] == 1) &  # 沪深300成分股
    (df['净利润TTM'] > 0) &       # 盈利
    (df['流通市值'] > 1000000)    # 市值大于100亿
)

selected_stocks = df[conditions]
```

## 📞 技术支持

### 问题排查
1. **查看日志**：使用`--log_level DEBUG`获取详细信息
2. **运行测试**：使用`python 综合测试套件.py`诊断问题
3. **检查配置**：确认Token和环境配置正确
4. **网络检查**：确保可以访问Tushare API

### 常见错误
- **"未配置 Tushare Token"**：需要设置有效的Token
- **"QMT连接失败"**：确保MiniQMT客户端已启动
- **"权限不足"**：需要足够的Tushare积分
- **"数据获取失败"**：检查网络连接和API限制

### 获取帮助
- 查看详细文档：`使用说明.md`
- 运行示例代码：`申万行业分类示例.py`、`指数成分示例.py`
- 检查测试结果：运行各种测试脚本

## 🎉 开始使用

现在您已经了解了基本使用方法，可以开始获取您需要的股票数据了！

```bash
# 开始您的第一次数据获取
python 下载日数据.py --trade_date 20231108 --limit 10
```

祝您使用愉快！
