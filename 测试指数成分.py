#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试指数成分功能
使用新的index_weight接口
"""

import pandas as pd
import tushare as ts
from 下载日数据 import ComprehensiveStockDataProcessor, Config

def test_index_weight_api():
    """测试指数成分和权重API"""
    print("=" * 60)
    print("测试指数成分和权重API")
    print("=" * 60)
    
    # 初始化
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    pro = processor.pro
    
    trade_date = "20231108"
    
    print(f"\n1. 测试index_weight接口 (交易日期: {trade_date})...")
    
    # 测试各个指数
    for index_name, index_code in config.INDEX_CODES.items():
        try:
            print(f"\n获取 {index_name} ({index_code}) 成分股...")
            
            # 获取指定日期的成分股和权重
            w = pro.index_weight(
                index_code=index_code,
                trade_date=trade_date
            )
            
            if w is not None and not w.empty:
                print(f"✓ 成功获取 {len(w)} 只成分股")
                print(f"  权重范围: {w['weight'].min():.4f} - {w['weight'].max():.4f}")
                print(f"  权重总和: {w['weight'].sum():.2f}%")
                
                # 显示前5只权重最大的股票
                top5 = w.nlargest(5, 'weight')
                print("  前5大权重股票:")
                for _, row in top5.iterrows():
                    print(f"    {row['con_code']}: {row['weight']:.4f}%")
                    
            else:
                print(f"⚠ 未获取到 {index_name} 在 {trade_date} 的成分股数据")
                
                # 尝试获取最近一个月的数据
                import datetime as dt
                trade_dt = dt.datetime.strptime(trade_date, '%Y%m%d')
                start_date = (trade_dt - dt.timedelta(days=30)).strftime('%Y%m%d')
                
                w_month = pro.index_weight(
                    index_code=index_code,
                    start_date=start_date,
                    end_date=trade_date
                )
                
                if w_month is not None and not w_month.empty:
                    latest_date = w_month['trade_date'].max()
                    latest_data = w_month[w_month['trade_date'] == latest_date]
                    print(f"  找到最近数据: {latest_date} ({len(latest_data)} 只股票)")
                else:
                    print(f"  最近一个月也未找到数据")
                
        except Exception as e:
            print(f"❌ 获取 {index_name} 失败: {e}")
    
    return True

def test_index_membership_methods():
    """测试指数成分标识方法"""
    print("\n" + "=" * 60)
    print("测试指数成分标识方法")
    print("=" * 60)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    # 测试股票列表 - 选择一些知名的大盘股
    test_stocks = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SH",  # 浦发银行
        "600036.SH",  # 招商银行
        "600519.SH",  # 贵州茅台
        "000858.SZ",  # 五粮液
        "600276.SH",  # 恒瑞医药
        "000725.SZ",  # 京东方A
        "002415.SZ",  # 海康威视
        "300059.SZ",  # 东方财富
    ]
    
    trade_date = "20231108"
    
    try:
        print(f"\n1. 测试直接获取方法...")
        idx_result1 = processor.get_index_membership_flags(trade_date, test_stocks)
        print(f"✓ 直接获取完成，共 {len(idx_result1)} 条记录")
        print("结果样例:")
        print(idx_result1)
        
        print(f"\n2. 测试批量获取方法...")
        idx_result2 = processor.get_index_membership_flags_batch(trade_date, test_stocks)
        print(f"✓ 批量获取完成，共 {len(idx_result2)} 条记录")
        print("结果样例:")
        print(idx_result2)
        
        print(f"\n3. 比较两种方法的结果...")
        # 比较结果是否一致
        index_cols = list(config.INDEX_CODES.keys())
        
        for col in index_cols:
            match_count = (idx_result1[col] == idx_result2[col]).sum()
            total_count = len(idx_result1)
            print(f"  {col}: {match_count}/{total_count} 匹配")
        
        # 统计各指数的成分股数量
        print(f"\n4. 指数成分股统计（直接方法）:")
        for col in index_cols:
            count = idx_result1[col].sum()
            print(f"  {col}: {count} 只")
        
        print(f"\n5. 指数成分股统计（批量方法）:")
        for col in index_cols:
            count = idx_result2[col].sum()
            print(f"  {col}: {count} 只")
        
        # 保存测试结果
        output_file = f"指数成分测试结果_{trade_date}.csv"
        
        # 合并两种方法的结果进行对比
        comparison_df = idx_result1.copy()
        for col in index_cols:
            comparison_df[f"{col}_批量"] = idx_result2[col]
        
        comparison_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 测试结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_index_analysis():
    """测试特定指数的详细分析"""
    print("\n" + "=" * 60)
    print("测试特定指数的详细分析")
    print("=" * 60)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    pro = processor.pro
    
    # 重点分析沪深300指数
    index_code = "399300.SZ"
    index_name = "沪深300"
    trade_date = "20231108"
    
    try:
        print(f"详细分析 {index_name} 指数...")
        
        # 获取成分股和权重
        w = pro.index_weight(
            index_code=index_code,
            trade_date=trade_date
        )
        
        if w is not None and not w.empty:
            print(f"✓ {index_name} 包含 {len(w)} 只成分股")
            
            # 权重分析
            print(f"\n权重分析:")
            print(f"  最大权重: {w['weight'].max():.4f}%")
            print(f"  最小权重: {w['weight'].min():.4f}%")
            print(f"  平均权重: {w['weight'].mean():.4f}%")
            print(f"  权重总和: {w['weight'].sum():.2f}%")
            
            # 前10大权重股票
            print(f"\n前10大权重股票:")
            top10 = w.nlargest(10, 'weight')
            for i, (_, row) in enumerate(top10.iterrows(), 1):
                print(f"  {i:2d}. {row['con_code']}: {row['weight']:.4f}%")
            
            # 权重分布分析
            print(f"\n权重分布:")
            bins = [0, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
            labels = ['<0.1%', '0.1-0.5%', '0.5-1.0%', '1.0-2.0%', '2.0-5.0%', '>5.0%']
            
            w['weight_range'] = pd.cut(w['weight'], bins=bins, labels=labels, right=False)
            weight_dist = w['weight_range'].value_counts().sort_index()
            
            for range_label, count in weight_dist.items():
                percentage = count / len(w) * 100
                print(f"  {range_label}: {count} 只 ({percentage:.1f}%)")
            
            # 保存详细分析结果
            output_file = f"{index_name}指数成分分析_{trade_date}.csv"
            w_analysis = w.copy()
            w_analysis['weight_range'] = pd.cut(w_analysis['weight'], bins=bins, labels=labels, right=False)
            w_analysis.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n✓ 详细分析结果已保存到: {output_file}")
            
        else:
            print(f"⚠ 未获取到 {index_name} 在 {trade_date} 的数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 详细分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_with_index():
    """测试完整数据获取（重点关注指数成分）"""
    print("\n" + "=" * 60)
    print("测试完整数据获取（重点关注指数成分）")
    print("=" * 60)
    
    config = Config()
    processor = ComprehensiveStockDataProcessor(config)
    
    trade_date = "20231108"
    
    try:
        print(f"构建 {trade_date} 的完整数据集（限制前30只股票）...")
        df = processor.build_comprehensive_dataset(trade_date, limit=30)
        
        print(f"✓ 数据集构建完成，共 {len(df)} 行数据")
        
        # 检查指数成分数据质量
        index_cols = list(config.INDEX_CODES.keys())
        
        print(f"\n指数成分数据质量统计:")
        for col in index_cols:
            member_count = df[col].sum()
            print(f"  {col}: {member_count}/{len(df)} ({member_count/len(df)*100:.1f}%)")
        
        # 显示指数成分分布
        print(f"\n指数成分重叠分析:")
        df['指数成分总数'] = df[index_cols].sum(axis=1)
        overlap_dist = df['指数成分总数'].value_counts().sort_index()
        
        for count, stocks in overlap_dist.items():
            print(f"  属于 {count} 个指数: {stocks} 只股票")
        
        # 找出属于多个指数的股票
        multi_index_stocks = df[df['指数成分总数'] > 1]
        if not multi_index_stocks.empty:
            print(f"\n属于多个指数的股票:")
            for _, row in multi_index_stocks.iterrows():
                indices = [col for col in index_cols if row[col] == 1]
                print(f"  {row['股票代码']} ({row['股票名称']}): {', '.join(indices)}")
        
        # 保存结果
        output_file = f"完整数据_含指数成分_{trade_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✓ 完整数据已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试指数成分功能...")
    
    # 1. 测试API
    success1 = test_index_weight_api()
    
    if success1:
        # 2. 测试方法
        success2 = test_index_membership_methods()
        
        if success2:
            # 3. 测试详细分析
            success3 = test_specific_index_analysis()
            
            if success3:
                # 4. 测试完整功能
                success4 = test_comprehensive_with_index()
                
                if all([success1, success2, success3, success4]):
                    print("\n🎉 所有指数成分测试通过！")
                    print("新的index_weight接口工作正常。")
                else:
                    print("\n⚠ 部分测试失败，请检查配置。")
            else:
                print("\n❌ 详细分析测试失败。")
        else:
            print("\n❌ 方法测试失败。")
    else:
        print("\n❌ API测试失败，请检查Tushare权限。")
