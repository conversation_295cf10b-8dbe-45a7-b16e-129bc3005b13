
import datetime
from xtquant import xtdata
import pandas as pd
import tushare as ts
token='5154467945318d04b93f4e49e19fa24efcccedae10c8802936aadf47'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed

# 数据年份
year = 2020
# dividend_type: 复权类型, qfq, hfq, none
dividend_type = 'none'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1m'

def download_data(stock, period, year):
    try:
        xtdata.download_history_data(stock_code = stock, 
                                     period = period,
                                     start_time = str(year) + '0101',
                                     end_time = str(year) + '1231')
    except:
        print(stock, 'data failed')

# 1 获取该时段内所有股票的列表
print('获取股票列表...')
trade_day = pro.trade_cal(exchange='', start_date=str(year)+'0101', end_date=str(year)+'1231')
trade_day = trade_day[trade_day['is_open'] == 1]
stock_list = []
for i, day in enumerate(trade_day['cal_date']):
    # 每隔20天取一次数据
    if i % 20 == 0:
        df = pro.daily(trade_date=day)
        stock_list += df['ts_code'].tolist()
        stock_list = list(set(stock_list))
print('获取股票列表完成')
print('股票个数: ', len(stock_list))


# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=4)(delayed(download_data)(stock, period, year) for stock in stock_list)   
print('下载数据完成') 

# 3 整理数据，将下载的加密数据整理成DataFrame保存成本地.csv文件
print('整理数据...')
folder_path = 'data/' + period + '/' + dividend_type + '_' + str(year)
if not os.path.exists(folder_path): os.makedirs(folder_path)
for i, stock in enumerate(stock_list):
    # 隔50个打印下进度
    if i%50 == 0: print(i,stock)
    data = xtdata.get_local_data(field_list=['time', 'open','close','high','low','volume',
                                             'amount','settelementPrice', 'openInterest',
                                             'preClose', 'suspendFlag'],
                                 stock_list=[stock],
                                 period=period,
                                 dividend_type=dividend_type)
    df = data[stock]
    try:
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df = df[df['datetime'] >= pd.to_datetime(str(year) + '0101')]
        df = df[df['datetime'] < pd.to_datetime(str(year+1) + '0101')]
        df.index = df['datetime']
        df = df[['open','close','high','low','volume','amount','preClose','suspendFlag']]
        df.loc[:,'code'] = stock
        df.to_csv(folder_path +'/' + stock + '.csv')
    except:
        pass
print('数据整理完成')
  
    
    
